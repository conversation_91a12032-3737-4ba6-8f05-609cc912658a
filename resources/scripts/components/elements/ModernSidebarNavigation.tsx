import React, { useEffect, useRef } from 'react';
import styled from 'styled-components/macro';
import tw from 'twin.macro';

const SidebarNavigationWrapper = styled.div`
    ${tw`hidden`}; // Hide by default, will be shown by JavaScript integration
    
    &.sidebar-integrated {
        ${tw`block`};
    }
`;

const NavigationItem = styled.div`
    ${tw`mb-1`};
`;

const NavigationLink = styled.a<{ $active?: boolean }>`
    ${tw`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200`};
    ${tw`text-gray-300 hover:text-white hover:bg-blue-600`};
    
    ${props => props.$active && tw`text-white bg-gradient-to-r from-blue-600 to-blue-700 shadow-lg`};
    
    &:hover {
        ${tw`transform translate-x-1`};
        box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
    }
    
    i {
        ${tw`w-5 h-5 mr-3 flex-shrink-0`};
    }
    
    span {
        ${tw`truncate`};
    }
`;

interface NavigationItemProps {
    href: string;
    icon?: string;
    children: React.ReactNode;
    active?: boolean;
}

const ModernNavigationItem: React.FC<NavigationItemProps> = ({ href, icon, children, active = false }) => (
    <NavigationItem>
        <NavigationLink href={href} $active={active}>
            {icon && <i className={`fa ${icon}`} />}
            <span>{children}</span>
        </NavigationLink>
    </NavigationItem>
);

interface ModernSidebarNavigationProps {
    children: React.ReactNode;
    className?: string;
}

const ModernSidebarNavigation: React.FC<ModernSidebarNavigationProps> = ({ children, className }) => {
    const wrapperRef = useRef<HTMLDivElement>(null);
    
    useEffect(() => {
        // Integration with the modern sidebar JavaScript
        const integrateWithSidebar = () => {
            const serverNavContent = document.getElementById('serverNavContent');
            const serverNavSection = document.getElementById('serverNavSection');
            
            if (serverNavContent && wrapperRef.current) {
                // Clear existing content
                serverNavContent.innerHTML = '';
                
                // Extract navigation items from React component
                const navItems = wrapperRef.current.querySelectorAll('a');
                
                navItems.forEach(link => {
                    const navItem = document.createElement('div');
                    navItem.className = 'nav-item';
                    
                    const navLink = document.createElement('a');
                    navLink.className = `nav-link ${link.classList.contains('active') ? 'active' : ''}`;
                    navLink.href = link.href;
                    
                    // Extract icon
                    const icon = link.querySelector('i');
                    if (icon) {
                        const iconElement = document.createElement('i');
                        iconElement.className = `${icon.className} nav-icon`;
                        navLink.appendChild(iconElement);
                    }
                    
                    // Extract text
                    const textElement = document.createElement('span');
                    textElement.className = 'nav-text';
                    textElement.textContent = link.textContent?.trim() || '';
                    navLink.appendChild(textElement);
                    
                    // Add click handler
                    navLink.addEventListener('click', (e) => {
                        // Remove active class from all server nav links
                        serverNavContent.querySelectorAll('.nav-link').forEach(l => {
                            l.classList.remove('active');
                        });
                        
                        // Add active class to clicked link
                        navLink.classList.add('active');
                    });
                    
                    navItem.appendChild(navLink);
                    serverNavContent.appendChild(navItem);
                });
                
                // Show server navigation section
                if (serverNavSection) {
                    serverNavSection.style.display = 'block';
                }
                
                // Hide the React component since it's now integrated
                if (wrapperRef.current) {
                    wrapperRef.current.style.display = 'none';
                }
            }
        };
        
        // Wait for the modern sidebar to be initialized
        const checkForSidebar = () => {
            if (document.getElementById('serverNavContent')) {
                integrateWithSidebar();
            } else {
                setTimeout(checkForSidebar, 100);
            }
        };
        
        checkForSidebar();
        
        // Cleanup function
        return () => {
            const serverNavSection = document.getElementById('serverNavSection');
            if (serverNavSection) {
                serverNavSection.style.display = 'none';
            }
        };
    }, [children]);
    
    return (
        <SidebarNavigationWrapper ref={wrapperRef} className={className}>
            {children}
        </SidebarNavigationWrapper>
    );
};

// Export both the wrapper and individual item component
export default ModernSidebarNavigation;
export { ModernNavigationItem };

// Also create a compatibility layer for existing SubNavigation usage
export const SubNavigationCompat: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    return <ModernSidebarNavigation>{children}</ModernSidebarNavigation>;
};
