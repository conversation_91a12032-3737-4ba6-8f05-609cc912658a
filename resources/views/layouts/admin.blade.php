<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <title>{{ config('app.name', 'Pterodactyl') }} - @yield('title')</title>
        <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
        <meta name="_token" content="{{ csrf_token() }}">

        <link rel="apple-touch-icon" sizes="180x180" href="/favicons/apple-touch-icon.png">
        <link rel="icon" type="image/png" href="/favicons/favicon-32x32.png" sizes="32x32">
        <link rel="icon" type="image/png" href="/favicons/favicon-16x16.png" sizes="16x16">
        <link rel="manifest" href="/favicons/manifest.json">
        <link rel="mask-icon" href="/favicons/safari-pinned-tab.svg" color="#bc6e3c">
        <link rel="shortcut icon" href="/favicons/favicon.ico">
        <meta name="msapplication-config" content="/favicons/browserconfig.xml">
        <meta name="theme-color" content="#0e4688">

        @include('layouts.scripts')

        @section('scripts')
            {!! Theme::css('vendor/select2/select2.min.css?t={cache-version}') !!}
            {!! Theme::css('vendor/bootstrap/bootstrap.min.css?t={cache-version}') !!}
            {!! Theme::css('vendor/adminlte/admin.min.css?t={cache-version}') !!}
            {!! Theme::css('vendor/adminlte/colors/skin-blue.min.css?t={cache-version}') !!}
            {!! Theme::css('vendor/sweetalert/sweetalert.min.css?t={cache-version}') !!}
            {!! Theme::css('vendor/animate/animate.min.css?t={cache-version}') !!}
            {!! Theme::css('css/pterodactyl.css?t={cache-version}') !!}
            {!! Theme::css('css/modern-dark-theme.css?t={cache-version}') !!}
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/ionicons/2.0.1/css/ionicons.min.css">
            <link rel="preconnect" href="https://fonts.googleapis.com">
            <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
            <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

            <!--[if lt IE 9]>
            <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
            <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
            <![endif]-->
        @show
    </head>
    <body class="modern-theme">
        <div class="wrapper">
            <!-- Modern Sidebar -->
            <aside class="modern-sidebar" id="modernSidebar">
                <div class="sidebar-header">
                    <a href="{{ route('index') }}" class="logo">
                        <span>{{ config('app.name', 'Pterodactyl') }}</span>
                    </a>
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fa fa-bars"></i>
                    </button>
                </div>

                <nav class="sidebar-nav">
                    <div class="nav-section">
                        <div class="nav-section-title">Administration</div>
                        <div class="nav-item">
                            <a href="{{ route('admin.index') }}" class="nav-link {{ Route::currentRouteName() !== 'admin.index' ?: 'active' }}">
                                <i class="fa fa-home nav-icon"></i>
                                <span class="nav-text">Overview</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="{{ route('admin.settings') }}" class="nav-link {{ ! starts_with(Route::currentRouteName(), 'admin.settings') ?: 'active' }}">
                                <i class="fa fa-wrench nav-icon"></i>
                                <span class="nav-text">Settings</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="{{ route('admin.api.index') }}" class="nav-link {{ ! starts_with(Route::currentRouteName(), 'admin.api') ?: 'active' }}">
                                <i class="fa fa-gamepad nav-icon"></i>
                                <span class="nav-text">Application API</span>
                            </a>
                        </div>
                    </div>

                    <div class="nav-section">
                        <div class="nav-section-title">Management</div>
                        <div class="nav-item">
                            <a href="{{ route('admin.databases') }}" class="nav-link {{ ! starts_with(Route::currentRouteName(), 'admin.databases') ?: 'active' }}">
                                <i class="fa fa-database nav-icon"></i>
                                <span class="nav-text">Databases</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="{{ route('admin.locations') }}" class="nav-link {{ ! starts_with(Route::currentRouteName(), 'admin.locations') ?: 'active' }}">
                                <i class="fa fa-globe nav-icon"></i>
                                <span class="nav-text">Locations</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="{{ route('admin.nodes') }}" class="nav-link {{ ! starts_with(Route::currentRouteName(), 'admin.nodes') ?: 'active' }}">
                                <i class="fa fa-sitemap nav-icon"></i>
                                <span class="nav-text">Nodes</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="{{ route('admin.servers') }}" class="nav-link {{ ! starts_with(Route::currentRouteName(), 'admin.servers') ?: 'active' }}">
                                <i class="fa fa-server nav-icon"></i>
                                <span class="nav-text">Servers</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="{{ route('admin.users') }}" class="nav-link {{ ! starts_with(Route::currentRouteName(), 'admin.users') ?: 'active' }}">
                                <i class="fa fa-users nav-icon"></i>
                                <span class="nav-text">Users</span>
                            </a>
                        </div>
                    </div>

                    <div class="nav-section">
                        <div class="nav-section-title">Services</div>
                        <div class="nav-item">
                            <a href="{{ route('admin.mounts') }}" class="nav-link {{ ! starts_with(Route::currentRouteName(), 'admin.mounts') ?: 'active' }}">
                                <i class="fa fa-magic nav-icon"></i>
                                <span class="nav-text">Mounts</span>
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="{{ route('admin.nests') }}" class="nav-link {{ ! starts_with(Route::currentRouteName(), 'admin.nests') ?: 'active' }}">
                                <i class="fa fa-th-large nav-icon"></i>
                                <span class="nav-text">Nests</span>
                            </a>
                        </div>
                    </div>
                </nav>

                <!-- Server Navigation Section (will be populated by JavaScript for server pages) -->
                <div class="server-nav-section" id="serverNavSection" style="display: none;">
                    <div class="nav-section-title">Server Controls</div>
                    <div id="serverNavContent">
                        <!-- Server navigation will be injected here -->
                    </div>
                </div>
            </aside>

            <!-- Top Navigation Bar -->
            <div class="main-content" id="mainContent">
                <nav class="top-nav">
                    <div class="nav-breadcrumb">
                        <h1>@yield('title', 'Dashboard')</h1>
                    </div>
                    <div class="nav-actions">
                        <a href="{{ route('account') }}" class="nav-action" data-toggle="tooltip" data-placement="bottom" title="Account Settings">
                            <img src="https://www.gravatar.com/avatar/{{ md5(strtolower(Auth::user()->email)) }}?s=32" class="user-avatar" alt="User Avatar" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 0.5rem;">
                            <span class="hidden-xs">{{ Auth::user()->name_first }} {{ Auth::user()->name_last }}</span>
                        </a>
                        <a href="{{ route('index') }}" class="nav-action" data-toggle="tooltip" data-placement="bottom" title="Exit Admin Control">
                            <i class="fa fa-server"></i>
                        </a>
                        <a href="{{ route('auth.logout') }}" id="logoutButton" class="nav-action" data-toggle="tooltip" data-placement="bottom" title="Logout">
                            <i class="fa fa-sign-out"></i>
                        </a>
                    </div>
                </nav>
                <!-- Main Content Area -->
                <div class="content-wrapper">
                    <section class="content-header">
                        @yield('content-header')
                    </section>
                    <section class="content">
                        <div class="row">
                            <div class="col-xs-12">
                                @if (count($errors) > 0)
                                    <div class="alert alert-danger fade-in">
                                        There was an error validating the data provided.<br><br>
                                        <ul>
                                            @foreach ($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif
                                @foreach (Alert::getMessages() as $type => $messages)
                                    @foreach ($messages as $message)
                                        <div class="alert alert-{{ $type }} alert-dismissable fade-in" role="alert">
                                            {!! $message !!}
                                        </div>
                                    @endforeach
                                @endforeach
                            </div>
                        </div>
                        @yield('content')
                    </section>
                </div>

                <footer class="main-footer">
                    <div class="pull-right small text-gray" style="margin-right:10px;margin-top:-7px;">
                        <strong><i class="fa fa-fw {{ $appIsGit ? 'fa-git-square' : 'fa-code-fork' }}"></i></strong> {{ $appVersion }}<br />
                        <strong><i class="fa fa-fw fa-clock-o"></i></strong> {{ round(microtime(true) - LARAVEL_START, 3) }}s
                    </div>
                    Copyright &copy; 2015 - {{ date('Y') }}  <a href="https://pterodactyl.io/">Pterodactyl Software - Velaire Hosting</a>.
                </footer>
            </div>
        </div>
        @section('footer-scripts')
            <script src="/js/keyboard.polyfill.js" type="application/javascript"></script>
            <script>keyboardeventKeyPolyfill.polyfill();</script>

            {!! Theme::js('vendor/jquery/jquery.min.js?t={cache-version}') !!}
            {!! Theme::js('vendor/sweetalert/sweetalert.min.js?t={cache-version}') !!}
            {!! Theme::js('vendor/bootstrap/bootstrap.min.js?t={cache-version}') !!}
            {!! Theme::js('vendor/slimscroll/jquery.slimscroll.min.js?t={cache-version}') !!}
            {!! Theme::js('vendor/adminlte/app.min.js?t={cache-version}') !!}
            {!! Theme::js('vendor/bootstrap-notify/bootstrap-notify.min.js?t={cache-version}') !!}
            {!! Theme::js('vendor/select2/select2.full.min.js?t={cache-version}') !!}
            {!! Theme::js('js/admin/functions.js?t={cache-version}') !!}
            {!! Theme::js('js/modern-sidebar.js?t={cache-version}') !!}
            <script src="/js/autocomplete.js" type="application/javascript"></script>

            @if(Auth::user()->root_admin)
                <script>
                    $('#logoutButton').on('click', function (event) {
                        event.preventDefault();

                        var that = this;
                        swal({
                            title: 'Do you want to log out?',
                            type: 'warning',
                            showCancelButton: true,
                            confirmButtonColor: '#d9534f',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'Log out'
                        }, function () {
                             $.ajax({
                                type: 'POST',
                                url: '{{ route('auth.logout') }}',
                                data: {
                                    _token: '{{ csrf_token() }}'
                                },complete: function () {
                                    window.location.href = '{{route('auth.login')}}';
                                }
                        });
                    });
                });
                </script>
            @endif

            <script>
                $(function () {
                    $('[data-toggle="tooltip"]').tooltip();

                    // Modern Sidebar Toggle Functionality
                    const sidebar = document.getElementById('modernSidebar');
                    const mainContent = document.getElementById('mainContent');
                    const sidebarToggle = document.getElementById('sidebarToggle');

                    // Load sidebar state from localStorage
                    const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
                    if (sidebarCollapsed) {
                        sidebar.classList.add('collapsed');
                        document.body.classList.add('sidebar-collapsed');
                    }

                    // Toggle sidebar
                    sidebarToggle.addEventListener('click', function() {
                        sidebar.classList.toggle('collapsed');
                        document.body.classList.toggle('sidebar-collapsed');

                        // Save state to localStorage
                        const isCollapsed = sidebar.classList.contains('collapsed');
                        localStorage.setItem('sidebarCollapsed', isCollapsed);

                        // Trigger resize event for any charts or components that need to adjust
                        setTimeout(() => {
                            window.dispatchEvent(new Event('resize'));
                        }, 300);
                    });

                    // Create mobile overlay
                    const mobileOverlay = document.createElement('div');
                    mobileOverlay.className = 'mobile-overlay';
                    document.body.appendChild(mobileOverlay);

                    // Mobile sidebar handling
                    function handleMobileView() {
                        if (window.innerWidth <= 768) {
                            document.body.classList.add('mobile-view');
                            // Close sidebar on mobile by default
                            sidebar.classList.remove('mobile-open');
                            mobileOverlay.classList.remove('active');
                        } else {
                            document.body.classList.remove('mobile-view');
                            sidebar.classList.remove('mobile-open');
                            mobileOverlay.classList.remove('active');
                        }
                    }

                    // Handle mobile sidebar toggle
                    sidebarToggle.addEventListener('click', function(e) {
                        if (window.innerWidth <= 768) {
                            e.stopPropagation();
                            sidebar.classList.toggle('mobile-open');
                            mobileOverlay.classList.toggle('active');
                        }
                    });

                    // Close sidebar when clicking overlay on mobile
                    mobileOverlay.addEventListener('click', function() {
                        sidebar.classList.remove('mobile-open');
                        mobileOverlay.classList.remove('active');
                    });

                    // Close sidebar when clicking outside on mobile
                    document.addEventListener('click', function(e) {
                        if (window.innerWidth <= 768 &&
                            !sidebar.contains(e.target) &&
                            !sidebarToggle.contains(e.target) &&
                            sidebar.classList.contains('mobile-open')) {
                            sidebar.classList.remove('mobile-open');
                            mobileOverlay.classList.remove('active');
                        }
                    });

                    // Handle window resize
                    window.addEventListener('resize', handleMobileView);
                    handleMobileView();

                    // Add smooth animations to nav links
                    document.querySelectorAll('.nav-link').forEach(link => {
                        link.addEventListener('mouseenter', function() {
                            this.style.transform = 'translateX(4px)';
                        });

                        link.addEventListener('mouseleave', function() {
                            if (!this.classList.contains('active')) {
                                this.style.transform = 'translateX(0)';
                            }
                        });
                    });

                    // Add fade-in animation to content
                    const contentElements = document.querySelectorAll('.content-wrapper > *');
                    contentElements.forEach((element, index) => {
                        element.style.animationDelay = `${index * 0.1}s`;
                        element.classList.add('fade-in');
                    });
                });
            </script>
        @show
    </body>
</html>
