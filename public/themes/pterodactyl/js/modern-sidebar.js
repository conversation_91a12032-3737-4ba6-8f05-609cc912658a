/**
 * Modern Sidebar Navigation Handler
 * Handles server navigation integration with the modern sidebar
 */

class ModernSidebar {
    constructor() {
        this.sidebar = document.getElementById('modernSidebar');
        this.serverNavSection = document.getElementById('serverNavSection');
        this.serverNavContent = document.getElementById('serverNavContent');
        this.isServerPage = false;
        
        this.init();
    }
    
    init() {
        // Check if we're on a server page
        this.detectServerPage();
        
        // Initialize server navigation if needed
        if (this.isServerPage) {
            this.initServerNavigation();
        }
        
        // Listen for navigation changes (for SPA-like behavior)
        this.observeNavigationChanges();
    }
    
    detectServerPage() {
        // Check URL patterns that indicate server pages
        const path = window.location.pathname;
        this.isServerPage = path.includes('/server/') || 
                           path.includes('/admin/servers/view/') ||
                           document.querySelector('[data-server-uuid]') !== null;
    }
    
    initServerNavigation() {
        // Show the server navigation section
        if (this.serverNavSection) {
            this.serverNavSection.style.display = 'block';
        }
        
        // Look for existing SubNavigation and move it to sidebar
        this.moveSubNavigationToSidebar();
        
        // If no existing navigation found, create server navigation from routes
        if (!this.serverNavContent.children.length) {
            this.createServerNavigation();
        }
    }
    
    moveSubNavigationToSidebar() {
        // Find the existing SubNavigation component
        const subNav = document.querySelector('[class*="SubNavigation"]') || 
                      document.querySelector('.nav-tabs-custom') ||
                      document.querySelector('.sub-navigation');
        
        if (subNav) {
            // Extract navigation links
            const navLinks = subNav.querySelectorAll('a');
            
            navLinks.forEach(link => {
                const navItem = this.createSidebarNavItem(
                    link.textContent.trim(),
                    link.href,
                    link.classList.contains('active') || link.parentElement.classList.contains('active')
                );
                
                this.serverNavContent.appendChild(navItem);
            });
            
            // Hide the original navigation
            subNav.style.display = 'none';
        }
    }
    
    createServerNavigation() {
        // Default server navigation items (can be customized based on permissions)
        const serverNavItems = [
            { name: 'Console', href: '#console', icon: 'fa-terminal', active: false },
            { name: 'Files', href: '#files', icon: 'fa-folder', active: false },
            { name: 'Databases', href: '#databases', icon: 'fa-database', active: false },
            { name: 'Schedules', href: '#schedules', icon: 'fa-clock-o', active: false },
            { name: 'Users', href: '#users', icon: 'fa-users', active: false },
            { name: 'Backups', href: '#backups', icon: 'fa-archive', active: false },
            { name: 'Network', href: '#network', icon: 'fa-globe', active: false },
            { name: 'Startup', href: '#startup', icon: 'fa-play', active: false },
            { name: 'Settings', href: '#settings', icon: 'fa-cog', active: false },
            { name: 'Activity', href: '#activity', icon: 'fa-list', active: false }
        ];
        
        // Get current page to set active state
        const currentPath = window.location.pathname;
        
        serverNavItems.forEach(item => {
            // Simple active detection based on URL
            item.active = currentPath.includes(item.name.toLowerCase()) ||
                         (item.name === 'Console' && currentPath.endsWith('/'));
            
            const navItem = this.createSidebarNavItem(item.name, item.href, item.active, item.icon);
            this.serverNavContent.appendChild(navItem);
        });
    }
    
    createSidebarNavItem(name, href, active = false, icon = null) {
        const navItem = document.createElement('div');
        navItem.className = 'nav-item';
        
        const navLink = document.createElement('a');
        navLink.className = `nav-link ${active ? 'active' : ''}`;
        navLink.href = href;
        
        if (icon) {
            const iconElement = document.createElement('i');
            iconElement.className = `fa ${icon} nav-icon`;
            navLink.appendChild(iconElement);
        }
        
        const textElement = document.createElement('span');
        textElement.className = 'nav-text';
        textElement.textContent = name;
        navLink.appendChild(textElement);
        
        // Add click handler for smooth transitions
        navLink.addEventListener('click', (e) => {
            // Remove active class from all server nav links
            this.serverNavContent.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // Add active class to clicked link
            navLink.classList.add('active');
        });
        
        navItem.appendChild(navLink);
        return navItem;
    }
    
    observeNavigationChanges() {
        // Watch for URL changes (for SPA navigation)
        let currentUrl = window.location.href;
        
        const observer = new MutationObserver(() => {
            if (window.location.href !== currentUrl) {
                currentUrl = window.location.href;
                this.detectServerPage();
                
                if (this.isServerPage) {
                    this.initServerNavigation();
                } else {
                    this.hideServerNavigation();
                }
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // Also listen for popstate events
        window.addEventListener('popstate', () => {
            setTimeout(() => {
                this.detectServerPage();
                if (this.isServerPage) {
                    this.initServerNavigation();
                } else {
                    this.hideServerNavigation();
                }
            }, 100);
        });
    }
    
    hideServerNavigation() {
        if (this.serverNavSection) {
            this.serverNavSection.style.display = 'none';
        }
        
        // Clear server navigation content
        if (this.serverNavContent) {
            this.serverNavContent.innerHTML = '';
        }
        
        // Show original SubNavigation if it was hidden
        const subNav = document.querySelector('[class*="SubNavigation"][style*="display: none"]');
        if (subNav) {
            subNav.style.display = '';
        }
    }
    
    // Public method to manually add server navigation items
    addServerNavItem(name, href, active = false, icon = null) {
        if (!this.isServerPage) return;
        
        const navItem = this.createSidebarNavItem(name, href, active, icon);
        this.serverNavContent.appendChild(navItem);
    }
    
    // Public method to update active navigation item
    setActiveNavItem(href) {
        this.serverNavContent.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
            if (link.href === href || link.href.includes(href)) {
                link.classList.add('active');
            }
        });
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.modernSidebar = new ModernSidebar();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModernSidebar;
}
