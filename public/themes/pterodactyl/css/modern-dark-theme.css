/**
 * Modern Dark Theme for Pterodactyl Panel
 * Inspired by Arix theme aesthetic with modern dark colors and smooth animations
 */

/* AdminLTE Compatibility Overrides */
.skin-blue .wrapper,
.skin-blue .main-sidebar,
.skin-blue .left-side {
    background-color: transparent !important;
    box-shadow: none !important;
}

.skin-blue .main-header {
    display: none !important; /* Hide original header */
}

.skin-blue .main-sidebar {
    display: none !important; /* Hide original sidebar */
}

.content-wrapper {
    margin-left: 0 !important;
    background-color: transparent !important;
}

.main-footer {
    background: var(--dark-gray) !important;
    color: var(--text-secondary) !important;
    border-top: 1px solid var(--border-color) !important;
    margin-left: 0 !important;
}

/* Root Variables for Modern Dark Theme */
:root {
    --primary-black: #000000;
    --secondary-black: #0a0a0a;
    --primary-blue: #1e40af;
    --secondary-blue: #1d4ed8;
    --accent-blue: #3b82f6;
    --light-blue: #60a5fa;
    --dark-gray: #111827;
    --medium-gray: #1f2937;
    --light-gray: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --border-color: #374151;
    --hover-bg: #1f2937;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 60px;
}

/* Global Body Styling */
body {
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--secondary-black) 50%, var(--dark-gray) 100%);
    color: var(--text-primary);
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Modern Sidebar Styling */
.modern-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: var(--sidebar-width);
    background: linear-gradient(180deg, var(--primary-black) 0%, var(--secondary-black) 100%);
    border-right: 1px solid var(--border-color);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    overflow: hidden;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
}

.modern-sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.modern-sidebar .sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
}

.modern-sidebar .sidebar-header .logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.modern-sidebar .sidebar-header .logo:hover {
    color: var(--light-blue);
    transform: scale(1.05);
}

.modern-sidebar .sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.modern-sidebar .sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(180deg);
}

/* Sidebar Navigation */
.modern-sidebar .sidebar-nav {
    padding: 1rem 0;
    height: calc(100vh - 140px);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.modern-sidebar .sidebar-nav::-webkit-scrollbar {
    width: 4px;
}

.modern-sidebar .sidebar-nav::-webkit-scrollbar-track {
    background: transparent;
}

.modern-sidebar .sidebar-nav::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 2px;
}

.modern-sidebar .nav-section {
    margin-bottom: 2rem;
}

.modern-sidebar .nav-section-title {
    padding: 0 1.5rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--text-muted);
    opacity: 0;
    transition: all 0.3s ease;
}

.modern-sidebar:not(.collapsed) .nav-section-title {
    opacity: 1;
}

.modern-sidebar .nav-item {
    margin: 0.25rem 1rem;
}

.modern-sidebar .nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.modern-sidebar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(30, 64, 175, 0.1), transparent);
    transition: left 0.5s ease;
}

.modern-sidebar .nav-link:hover::before {
    left: 100%;
}

.modern-sidebar .nav-link:hover {
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

.modern-sidebar .nav-link.active {
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.4);
}

.modern-sidebar .nav-link .nav-icon {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.75rem;
    transition: all 0.3s ease;
}

.modern-sidebar.collapsed .nav-link .nav-icon {
    margin-right: 0;
}

.modern-sidebar .nav-link .nav-text {
    font-weight: 500;
    opacity: 1;
    transition: all 0.3s ease;
}

.modern-sidebar.collapsed .nav-link .nav-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

/* Server Navigation Section */
.modern-sidebar .server-nav-section {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(180deg, transparent 0%, var(--primary-black) 50%);
    padding: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Main Content Area */
.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    background: linear-gradient(135deg, var(--dark-gray) 0%, var(--medium-gray) 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-collapsed .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

/* Top Navigation Bar */
.top-nav {
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--secondary-black) 100%);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.top-nav .nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.top-nav .nav-actions .nav-action {
    padding: 0.5rem;
    border-radius: 0.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.top-nav .nav-actions .nav-action:hover {
    color: var(--text-primary);
    background: var(--hover-bg);
    transform: translateY(-2px);
}

/* Content Cards */
.content-card {
    background: linear-gradient(135deg, var(--medium-gray) 0%, var(--light-gray) 100%);
    border-radius: 1rem;
    padding: 2rem;
    margin: 1rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    border: 1px solid var(--border-color);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.content-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
}

/* Buttons */
.btn-modern {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--text-primary);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .modern-sidebar {
        width: 240px;
    }

    .main-content {
        margin-left: 240px;
    }

    .modern-sidebar.collapsed {
        width: var(--sidebar-collapsed-width);
    }

    .sidebar-collapsed .main-content {
        margin-left: var(--sidebar-collapsed-width);
    }
}

@media (max-width: 992px) {
    .modern-sidebar {
        width: 220px;
    }

    .main-content {
        margin-left: 220px;
    }

    .top-nav {
        padding: 1rem 1.5rem;
    }

    .content-card {
        margin: 0.5rem;
        padding: 1.5rem;
    }
}

@media (max-width: 768px) {
    :root {
        --sidebar-width: 280px;
    }

    .modern-sidebar {
        transform: translateX(-100%);
        width: var(--sidebar-width);
        z-index: 1050;
    }

    .modern-sidebar.mobile-open {
        transform: translateX(0);
        box-shadow: 8px 0 32px rgba(0, 0, 0, 0.5);
    }

    .main-content {
        margin-left: 0;
    }

    .sidebar-collapsed .main-content {
        margin-left: 0;
    }

    .top-nav {
        padding: 1rem;
    }

    .top-nav .nav-actions {
        gap: 0.5rem;
    }

    .top-nav .nav-actions .nav-action span {
        display: none;
    }

    .content-card {
        margin: 0.5rem;
        padding: 1rem;
        border-radius: 0.75rem;
    }

    /* Mobile-specific sidebar styles */
    .modern-sidebar .sidebar-header {
        padding: 1rem;
    }

    .modern-sidebar .nav-link {
        padding: 1rem;
    }

    .modern-sidebar .nav-link .nav-text {
        font-size: 0.9rem;
    }

    /* Mobile overlay */
    .mobile-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1040;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .mobile-overlay.active {
        opacity: 1;
        visibility: visible;
    }
}

@media (max-width: 576px) {
    .modern-sidebar {
        width: 100%;
    }

    .top-nav {
        padding: 0.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .top-nav .nav-breadcrumb h1 {
        font-size: 1.25rem;
    }

    .content-card {
        margin: 0.25rem;
        padding: 0.75rem;
        border-radius: 0.5rem;
    }

    .btn-modern {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    /* Stack form elements on mobile */
    .form-row {
        flex-direction: column;
    }

    .form-row > * {
        margin-bottom: 1rem;
    }

    /* Adjust table for mobile */
    .table-responsive {
        border-radius: 0.5rem;
    }

    .table th,
    .table td {
        padding: 0.5rem;
        font-size: 0.875rem;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .modern-sidebar .nav-link:hover {
        transform: none;
    }

    .modern-sidebar .nav-link:active {
        transform: scale(0.98);
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    }

    .btn-modern:hover {
        transform: none;
    }

    .btn-modern:active {
        transform: scale(0.98);
    }

    .content-card:hover {
        transform: none;
    }

    .hover-lift:hover {
        transform: none;
    }

    .hover-lift:active {
        transform: scale(0.98);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --primary-black: #000000;
        --secondary-black: #000000;
        --text-primary: #ffffff;
        --text-secondary: #ffffff;
        --border-color: #ffffff;
    }

    .modern-sidebar {
        border-right: 2px solid var(--border-color);
    }

    .nav-link {
        border: 1px solid transparent;
    }

    .nav-link:hover,
    .nav-link.active {
        border-color: var(--border-color);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .modern-sidebar {
        transition: none;
    }

    .nav-link {
        transition: none;
    }

    .btn-modern {
        transition: none;
    }

    .content-card {
        transition: none;
    }
}

/* Enhanced Interactive Elements */
.form-control, .form-select, input, textarea, select {
    background: linear-gradient(135deg, var(--medium-gray) 0%, var(--light-gray) 100%);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-control:focus, .form-select:focus, input:focus, textarea:focus, select:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
    outline: none;
    transform: translateY(-1px);
}

/* Enhanced Tables */
.table {
    background: linear-gradient(135deg, var(--medium-gray) 0%, var(--light-gray) 100%);
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.table th {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--text-primary);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.1em;
    padding: 1rem;
    border: none;
}

.table td {
    padding: 1rem;
    border-color: var(--border-color);
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: rgba(30, 64, 175, 0.1);
    transform: scale(1.01);
}

/* Enhanced Alerts */
.alert {
    border-radius: 1rem;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.alert-success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(34, 197, 94, 0.05) 100%);
    color: #22c55e;
    border-left: 4px solid #22c55e;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
    color: #ef4444;
    border-left: 4px solid #ef4444;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%);
    color: #f59e0b;
    border-left: 4px solid #f59e0b;
}

.alert-info {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
    color: #3b82f6;
    border-left: 4px solid #3b82f6;
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-blue);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Enhanced Modals */
.modal-content {
    background: linear-gradient(135deg, var(--medium-gray) 0%, var(--light-gray) 100%);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    border-bottom: 1px solid var(--border-color);
    border-radius: 1rem 1rem 0 0;
    padding: 1.5rem;
}

.modal-title {
    color: var(--text-primary);
    font-weight: 600;
}

.modal-body {
    padding: 2rem;
    color: var(--text-secondary);
}

.modal-footer {
    background: var(--dark-gray);
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 1rem 1rem;
    padding: 1.5rem;
}

/* Tooltip Enhancements */
.tooltip {
    font-size: 0.75rem;
}

.tooltip-inner {
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--secondary-black) 100%);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 0.5rem 0.75rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Progress Bars */
.progress {
    background: var(--dark-gray);
    border-radius: 1rem;
    overflow: hidden;
    height: 0.75rem;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-blue) 0%, var(--accent-blue) 100%);
    transition: width 0.6s ease;
    position: relative;
    overflow: hidden;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInLeft {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.slide-in-right {
    animation: slideInRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInRight {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.scale-in {
    animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes scaleIn {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.bounce-in {
    animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

/* Hover Effects */
.hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
}

.hover-glow {
    transition: all 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(30, 64, 175, 0.4);
}

/* Staggered Animations */
.stagger-animation > * {
    animation: fadeIn 0.5s ease-in-out;
    animation-fill-mode: both;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
