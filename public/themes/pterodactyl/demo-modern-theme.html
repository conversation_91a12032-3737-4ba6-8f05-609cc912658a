<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Dark Theme Demo - Pterodactyl</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/modern-dark-theme.css">
    <style>
        /* Demo-specific styles */
        .demo-content {
            padding: 2rem;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .demo-section {
            background: linear-gradient(135deg, var(--medium-gray) 0%, var(--light-gray) 100%);
            border-radius: 1rem;
            padding: 2rem;
            border: 1px solid var(--border-color);
        }
        
        .demo-section h3 {
            color: var(--text-primary);
            margin-bottom: 1rem;
            font-weight: 600;
        }
    </style>
</head>
<body class="modern-theme">
    <div class="wrapper">
        <!-- Modern Sidebar -->
        <aside class="modern-sidebar" id="modernSidebar">
            <div class="sidebar-header">
                <a href="#" class="logo">
                    <span>Pterodactyl</span>
                </a>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fa fa-bars"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Administration</div>
                    <div class="nav-item">
                        <a href="#" class="nav-link active">
                            <i class="fa fa-home nav-icon"></i>
                            <span class="nav-text">Overview</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fa fa-wrench nav-icon"></i>
                            <span class="nav-text">Settings</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fa fa-gamepad nav-icon"></i>
                            <span class="nav-text">Application API</span>
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Management</div>
                    <div class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fa fa-database nav-icon"></i>
                            <span class="nav-text">Databases</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fa fa-globe nav-icon"></i>
                            <span class="nav-text">Locations</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fa fa-sitemap nav-icon"></i>
                            <span class="nav-text">Nodes</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fa fa-server nav-icon"></i>
                            <span class="nav-text">Servers</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fa fa-users nav-icon"></i>
                            <span class="nav-text">Users</span>
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Services</div>
                    <div class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fa fa-magic nav-icon"></i>
                            <span class="nav-text">Mounts</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fa fa-th-large nav-icon"></i>
                            <span class="nav-text">Nests</span>
                        </a>
                    </div>
                </div>
            </nav>

            <!-- Server Navigation Section -->
            <div class="server-nav-section" id="serverNavSection">
                <div class="nav-section-title">Server Controls</div>
                <div id="serverNavContent">
                    <div class="nav-item">
                        <a href="#" class="nav-link active">
                            <i class="fa fa-terminal nav-icon"></i>
                            <span class="nav-text">Console</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fa fa-folder nav-icon"></i>
                            <span class="nav-text">Files</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fa fa-cog nav-icon"></i>
                            <span class="nav-text">Settings</span>
                        </a>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <div class="main-content" id="mainContent">
            <nav class="top-nav">
                <div class="nav-breadcrumb">
                    <h1>Modern Dark Theme Demo</h1>
                </div>
                <div class="nav-actions">
                    <a href="#" class="nav-action">
                        <i class="fa fa-user"></i>
                        <span>Admin User</span>
                    </a>
                    <a href="#" class="nav-action">
                        <i class="fa fa-server"></i>
                    </a>
                    <a href="#" class="nav-action">
                        <i class="fa fa-sign-out"></i>
                    </a>
                </div>
            </nav>

            <div class="content-wrapper">
                <div class="demo-content">
                    <div class="content-card fade-in">
                        <h2 style="color: var(--text-primary); margin-bottom: 1rem;">Modern Dark Theme Features</h2>
                        <p style="color: var(--text-secondary); margin-bottom: 2rem;">
                            This theme features a modern dark design with smooth animations, responsive layout, and professional aesthetics.
                        </p>
                        
                        <div class="demo-grid stagger-animation">
                            <div class="demo-section hover-lift">
                                <h3><i class="fa fa-palette"></i> Color Scheme</h3>
                                <p style="color: var(--text-secondary);">
                                    Deep blacks (#000000, #0a0a0a) combined with professional blue accents (#1e40af) create a powerful, modern look.
                                </p>
                            </div>
                            
                            <div class="demo-section hover-lift">
                                <h3><i class="fa fa-mobile"></i> Responsive Design</h3>
                                <p style="color: var(--text-secondary);">
                                    Fully responsive layout that works perfectly on desktop, tablet, and mobile devices with touch-optimized interactions.
                                </p>
                            </div>
                            
                            <div class="demo-section hover-lift">
                                <h3><i class="fa fa-magic"></i> Smooth Animations</h3>
                                <p style="color: var(--text-secondary);">
                                    Subtle animations and transitions enhance the user experience without being distracting.
                                </p>
                            </div>
                            
                            <div class="demo-section hover-lift">
                                <h3><i class="fa fa-bars"></i> Collapsible Sidebar</h3>
                                <p style="color: var(--text-secondary);">
                                    Modern sidebar that can be collapsed for more screen space, with server navigation integrated at the bottom.
                                </p>
                            </div>
                        </div>
                        
                        <div style="margin-top: 2rem;">
                            <button class="btn-modern hover-glow">
                                <i class="fa fa-rocket"></i> Test Button
                            </button>
                            <button class="btn-modern" style="margin-left: 1rem; background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);">
                                <i class="fa fa-check"></i> Success Button
                            </button>
                        </div>
                        
                        <div style="margin-top: 2rem;">
                            <div class="alert alert-info">
                                <strong>Info:</strong> This is an example of the modern alert styling.
                            </div>
                            <div class="alert alert-success">
                                <strong>Success:</strong> Theme has been successfully applied!
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/modern-sidebar.js"></script>
    <script>
        // Demo-specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar toggle functionality
            const sidebar = document.getElementById('modernSidebar');
            const mainContent = document.getElementById('mainContent');
            const sidebarToggle = document.getElementById('sidebarToggle');
            
            // Load sidebar state from localStorage
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                sidebar.classList.add('collapsed');
                document.body.classList.add('sidebar-collapsed');
            }
            
            // Toggle sidebar
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                document.body.classList.toggle('sidebar-collapsed');
                
                // Save state to localStorage
                const isCollapsed = sidebar.classList.contains('collapsed');
                localStorage.setItem('sidebarCollapsed', isCollapsed);
            });
            
            // Add click handlers to nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all links in the same section
                    const section = this.closest('.nav-section, #serverNavContent');
                    if (section) {
                        section.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                    }
                    
                    // Add active class to clicked link
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
